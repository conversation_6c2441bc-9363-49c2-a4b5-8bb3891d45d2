{"name": "mmt", "version": "1.0.0", "description": "A Node.js project with Jest testing", "main": "src/main.js", "scripts": {"start": "node src/main.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"jest": "^29.7.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/main.js"], "testMatch": ["**/__tests__/**/*.js", "**/?(*.)+(spec|test).js"]}}