#!/bin/bash

# Set the correct PATH
export PATH="/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.bun/bin:/usr/local/bin:/Users/<USER>/Library/Android/sdk/tools/bin:/Users/<USER>/bin:/usr/local/bin:/Users/<USER>/.rbenv/shims:/Library/Frameworks/Python.framework/Versions/3.12/bin:/Users/<USER>/.local/share/solana/install/active_release/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Library/TeX/texbin:/Applications/Little Snitch.app/Contents/Components:/usr/local/share/dotnet:~/.dotnet/tools:/opt/podman/bin:/Users/<USER>/.codon/bin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand:/Users/<USER>/.dotnet/tools:/Users/<USER>/development/flutter/bin:/Users/<USER>/.local/bin:/Users/<USER>/.cache/lm-studio/bin:/Users/<USER>/.rvm/bin"

# Main run script
if [ "$1" = "test" ]; then
    npm test
else
    npm start
fi
